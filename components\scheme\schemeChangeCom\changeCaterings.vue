<script setup lang="ts">
// 方案变更-用餐方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  CateringTypeConstant,
  CateringTimeTypeConstant,
  HotelDinnerTypeConstant,
  HaveDrinksTypeConstantNum,
  HotelsArr,
  CateringsArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
  merchantType: {
    type: Number,
    default: null,
  },
  isCateringStandardControl: {
    // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeCateringsEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

const priceTip = ref<string>('');

// 用餐
const cateringsParams = ref<CateringsArr>({
  tempSchemeHotelId: null, // 需求酒店id
  isInsideHotel: null, // 是否酒店提供用餐
  demandDate: '', // 需求日期
  cateringType: null, // 餐饮类型
  cateringTime: null, // 用餐时间
  schemePersonNum: null, // 人数
  demandUnitPrice: null, // 用餐标准
  isIncludeDrinks: 0, // 是否包含酒水

  schemeUnitPrice: null, // 自动测算单价

  description: null,
});

// 价格计算
const priceCalcFun = () => {
  const priceWriteList = newSchemeList.value.filter(
    (e) => e.schemeUnitPrice && e.schemePersonNum && e.schemeUnitPrice >= 0 && e.schemePersonNum >= 0,
  );
  subtotal.value = 0;

  priceWriteList.forEach((e) => {
    subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
  });

  emit('schemePriceEmit', { type: 'catering', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

const schemePlanLabelList = ['餐饮提供方', '酒店位置', '用餐类型', '用餐时间', '人数', '餐标', '是否包含酒水', '备注'];

// 酒店名称
const hotelNameChange = (hotelItem: HotelsArr) => {
  let str = '-';

  props.hotels.forEach((e, index) => {
    if (e.id && (e.id === hotelItem.tempSchemeHotelId || e.id === hotelItem.miceSchemeHotelId)) {
      str = `酒店${index + 1}(${e.hotelName + '/' + (hotelLevelAllConstant.ofType(e.level)?.desc || '-')})`;
    }
  });

  return str;
};

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

const addScheme = (idx: number) => {
  isVerifyFailed.value = false;

  newSchemeList.value.push({
    isSchemeChangeAdd: true, // 是否变更方案新增

    ...cateringsParams.value,
    tempSchemeHotelId: props.hotels && props.hotels.length === 1 ? props.hotels[0].id : null,

    demandDate: oldSchemeList.value[0]?.demandDate || '',
  });

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const cateringTempSave = () => {
  emit('schemeCateringsEmit', {
    schemeCaterings: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const cateringSub = () => {
  let isCateringVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isCateringVerPassed === false) return;

    if (e.isInsideHotel === null || e.isInsideHotel === undefined) {
      message.error('请选择' + e.demandDate + '用餐' + (i + 1) + '餐饮提供方');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.isInsideHotel && !e.tempSchemeHotelId) {
      message.error('请选择' + e.demandDate + '用餐' + (i + 1) + '餐饮提供方');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.cateringType === null || e.cateringType === undefined) {
      message.error('请选择' + e.demandDate + '用餐' + (i + 1) + '用餐类型');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.cateringTime === null || e.cateringTime === undefined) {
      message.error('请选择' + e.demandDate + '用餐' + (i + 1) + '用餐时间');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.schemePersonNum === null || e.schemePersonNum === undefined) {
      message.error('请填写' + e.demandDate + '用餐' + (i + 1) + '人数');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.demandUnitPrice === null || e.demandUnitPrice === undefined) {
      message.error('请填写' + e.demandDate + '用餐' + (i + 1) + '餐标');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
      message.error('请填写' + e.demandDate + '用餐' + (i + 1) + '单价');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }
  });

  if (isCateringVerPassed) {
    cateringTempSave();
  }

  return isCateringVerPassed;
};

defineExpose({ cateringSub, cateringTempSave });

onMounted(async () => {
  if ((props.schemeItem && props.schemeItem.caterings) || (props.schemeCacheItem && props.schemeCacheItem.caterings)) {
    // console.log('%c [ 用餐 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeItem.caterings);

    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.caterings || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheItem?.caterings || [];
    } else if (props.isSchemeBargain && props.schemeCacheItem) {
      // 议价、议价查看
      newSchemeList.value = props.schemeCacheItem?.caterings || [];
    } else {
      // 变更查看
      newSchemeList.value = JSON.parse(JSON.stringify(oldSchemeList.value));
    }

    // 酒店反显
    newSchemeList.value.forEach((e, index) => {
      e.isIncludeDrinks = e.isIncludeDrinks ? 1 : 0;

      // 原价赋值
      e.oldSchemeUnitPrice = oldSchemeList.value[index]?.schemeUnitPrice || null;

      // isCateringStandardControl - 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
      // 餐标可以提高，但不能高于原价
      e.schemeUnitPrice = e.schemeUnitPrice || oldSchemeList.value[index]?.schemeUnitPrice || null;
      e.min = props.isCateringStandardControl === '2' ? oldSchemeList.value[index]?.demandUnitPrice : 0;
      e.max =
        props.schemeChangeType === 'schemeBargainingEdit'
          ? e.oldSchemeUnitPrice
          : props.isCateringStandardControl === '3'
          ? e.oldSchemeUnitPrice || oldSchemeList.value[index]?.demandUnitPrice
          : 999999.99;

      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1
            ? props.hotels[0].id
            : oldSchemeList.value[index]?.miceSchemeHotelId || props.hotels[0].id;
      } else {
        if (e.miceSchemeHotelId) e.tempSchemeHotelId = oldSchemeList.value[index]?.miceSchemeHotelId;
      }
    });

    priceTip.value =
      props.isCateringStandardControl === '1'
        ? '不可修改'
        : props.isCateringStandardControl === '2'
        ? '可以提高'
        : '可以降低';

    // 价格计算
    priceCalcFun();
  }
});
</script>

<template>
  <!-- 用餐方案 -->
  <div class="scheme_catering" v-if="oldSchemeList.length > 0 || newSchemeList.length > 0">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_title" v-show="oldSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>用餐方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用餐' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
                </template>
                {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.isInsideHotel ? hotelNameChange(item) : '-' }}
                </template>
                {{ item.isInsideHotel ? hotelNameChange(item) : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
                </template>
                {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
                </template>
                {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </template>
                {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 color_blue">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HaveDrinksTypeConstantNum.ofType(item.isIncludeDrinks)?.desc || '-' }}
                </template>
                {{ HaveDrinksTypeConstantNum.ofType(item.isIncludeDrinks)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ '¥' + formatNumberThousands(item.schemeUnitPrice) }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPrice && item.schemePersonNum
                    ? formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title" v-show="newSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>用餐方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用餐' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.isInsideHotel === null || item.isInsideHotel === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
                    </template>
                    {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.isInsideHotel"
                  style="width: 100%"
                  placeholder="请选择餐饮提供方"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in HotelDinnerTypeConstant.toArray()"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <a-tooltip
                class="pl12 pr12"
                placement="topLeft"
                v-if="
                  ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                  !item.isSchemeChangeAdd
                "
              >
                <template #title>
                  {{ hotelNameChange(item) }}
                </template>
                {{ hotelNameChange(item) }}
              </a-tooltip>
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.tempSchemeHotelId && item.isInsideHotel ? 'error_tip' : '',
                ]"
                v-else
              >
                <a-select
                  v-if="item.isInsideHotel"
                  style="width: 100%"
                  v-model:value="item.tempSchemeHotelId"
                  :disabled="
                    (hotels && hotels.length === 1) ||
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType)
                  "
                  placeholder="请选择酒店"
                  :bordered="false"
                  :dropdownMatchSelectWidth="300"
                  allow-clear
                >
                  <a-select-option v-for="(item, idx) in hotels" :key="item.id" :value="item.id">
                    <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                      {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>

                <span class="pl12" v-else>-</span>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.cateringType === null || item.cateringType === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
                    </template>
                    {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.cateringType"
                  style="width: 100%"
                  placeholder="请选择用餐类型"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in item.isInsideHotel
                      ? CateringTypeConstant.toArray().slice(0, 3)
                      : CateringTypeConstant.toArray().slice(3, 5)"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.cateringTime === null || item.cateringTime === undefined) ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
                    </template>
                    {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.cateringTime"
                  style="width: 100%"
                  placeholder="请选择用餐时间"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in CateringTimeTypeConstant.toArray()"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemePersonNum === null || item.schemePersonNum === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                    </template>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.schemePersonNum"
                    @blur="changePrice(idx)"
                    placeholder="请填写人数"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="999999"
                    :precision="0"
                  />
                  <span>人</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value color_blue">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.demandUnitPrice === null || item.demandUnitPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
                    </template>
                    {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.demandUnitPrice"
                    @blur="changePrice(idx)"
                    placeholder="请填写餐标"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999.99"
                    :precision="2"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0']">
                <div
                  class="pl12"
                  v-if="
                    ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                    !item.isSchemeChangeAdd
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ HaveDrinksTypeConstantNum.ofType(item.isIncludeDrinks)?.desc || '-' }}
                    </template>
                    {{ HaveDrinksTypeConstantNum.ofType(item.isIncludeDrinks)?.desc || '-' }}
                  </a-tooltip>
                </div>

                <a-select
                  v-else
                  v-model:value="item.isIncludeDrinks"
                  style="width: 100%"
                  placeholder="请选择是否包含酒水"
                  :bordered="false"
                  :dropdownMatchSelectWidth="90"
                >
                  <a-select-option
                    v-for="item in HaveDrinksTypeConstantNum.toArray()"
                    :key="item.code"
                    :value="item.code"
                  >
                    <a-tooltip placement="topLeft" :title="item.desc">
                      {{ item.desc }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  ['schemeBargainingEdit', 'schemeBargainingView', 'schemeChangeView'].includes(schemeChangeType) ||
                  !item.isSchemeChangeAdd
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>
              <a-tooltip placement="topLeft" v-else>
                <template #title v-if="item.description">
                  {{ item.description || '-' }}
                </template>
                <a-input
                  v-model:value="item.description"
                  style="width: calc(100% - 30px)"
                  placeholder="备注"
                  :maxlength="500"
                  :bordered="false"
                  allow-clear
                />
                <div class="scheme_plan_edit"></div>
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemeCateringId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="
                  ['schemeBargainingView', 'schemeChangeView', 'schemeWinBidView'].includes(schemeChangeType) ||
                  (!item.isSchemeChangeAdd && schemeChangeType === 'schemeChangeEdit')
                "
              >
                {{ '¥' + formatNumberThousands(item.schemeUnitPrice) }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.schemeUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.schemeUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="item.min"
                  :max="item.max"
                  :disabled="isCateringStandardControl === '1'"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeUnitPrice && item.schemePersonNum
                    ? formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>

            <!-- 操作 -->
            <div
              class="action_icons"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                schemeChangeType === 'schemeChangeEdit' &&
                newSchemeList.length > 1 &&
                item.isSchemeChangeAdd
              "
            >
              <a-popconfirm
                :title="'确认删除用餐' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div
          v-if="
            ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
            schemeChangeType === 'schemeChangeEdit'
          "
          class="add_scheme_plan mt20"
          @click="addScheme(index)"
        >
          <div class="plan_add_img mr8"></div>
          <span>新增用餐</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_catering {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_catering.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .p0 {
    padding: 0 !important;
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }

    :deep(.ant-input-number-disabled) {
      background: none;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
